# frozen_string_literal: true

module PerformanceReview
  module Calibration
    class ChartData < ApplicationService
      prepend SimpleCommand

      include Help<PERSON>

      def initialize(params)
        @review_cycle = ReviewCycle.find_by(id: params[:review_cycle_id])
        @current_user = User.find_by(id: params[:current_user_id])
        @calibration_question = ReviewCycleCalibrationQuestion.find_by(
          id: params[:calibration_question_id], review_cycle_id: @review_cycle&.id,
        )
        @value_type = params['value_type'] || 'discrete'
        @reviewer_type = reviewer_type_by_column_type(@calibration_question)
        @where_clauses = reviewee_filter_where_clauses(params)
      end

      def call
        return errors.add(:base, 'Review Cycle not found!') if review_cycle.blank?
        return errors.add(:base, 'Calibration question not found!') if calibration_question.blank?

        chart_data
      end

      private

      attr_reader :review_cycle, :calibration_question, :value_type, :reviewer_type, :where_clauses, :current_user

      def chart_data
        grouped_scores, grouped_final_scores, scores, final_scores = scores_by_column_type

        score_series = series_data_for(grouped_scores)
        final_score_series = series_data_for(grouped_final_scores)

        {
          categories: categories,
          score_series: score_series,
          final_score_series: final_score_series,
          score_series_percentage: series_data_percentage(score_series),
          final_score_series_percentage: series_data_percentage(final_score_series),
          average_score: fetch_average_for(scores),
          median_score: fetch_median_for(scores),
          average_final_score: fetch_average_for(final_scores),
          median_final_score: fetch_median_for(final_scores),
        }
      end

      def scores_by_column_type
        case calibration_question.column_type
        when 'question', 'custom_column', 'past_review_question'
          [
            question_responses,
            question_responses(final_score: true),
            question_responses_criteria.map(&:final_score),
            question_responses_criteria(final_score: true).map(&:final_score),
          ]
        when 'goals_score'
          [goal_responses, [], goal_responses_criteria.map(&:final_score), []]
        when 'competency_score'
          [competency_responses, [], competency_responses_criteria.map(&:final_score), []]
        else
          []
        end
      end

      def categories
        @_categories ||= if value_type == 'discrete'
          discrete_category_values(calibration_question)
        elsif value_type == 'continuous'
          ranges = continuous_score_ranges(calibration_question)
          ranges.map { |start, stop| "#{start}-#{stop}" }
        end
      end

      def series_data_for(scores)
        return [] if scores.blank?

        if value_type == 'discrete'
          discrete_scores = scores.each_with_object(Hash.new(0)) do |score, hash|
            rounded_score = score.final_score&.round
            next if rounded_score.nil?

            hash[rounded_score] += score.num_of_reviewees
          end
          categories.map { |c| discrete_scores[c.to_i] || 0 }
        elsif value_type == 'continuous'
          ranges = continuous_score_ranges(calibration_question)
          ranges.map do |range|
            scores.select { |s| (range.first..range.last).include?(s.final_score) }.sum(&:num_of_reviewees)
          end
        end
      end

      def series_data_percentage(series)
        return [] if series.blank?

        total_reviewees = series.sum
        series.map { |reviewee_count| nan_to_zero(((reviewee_count / total_reviewees.to_f) * 100).round(1)) }
      end

      def fetch_average_for(scores)
        return nil if scores.blank?

        scores.average.round(1)
      end

      def fetch_median_for(scores)
        return nil if scores.blank?

        scores.median.round(1)
      end

      # Question.find(78756).review_responses.count
      def question_responses(final_score: false)
        # For multi-reviewer auto-calculated questions, use the same pattern as goals/competencies
        if multi_reviewer_auto_calculated_question?
          reviewee_question_scores = question_responses_criteria(final_score: final_score).select(
            'review_responses.reviewee_id',
          ).group('review_responses.reviewee_id')

          ReviewResponse.unscoped.select('COUNT(t.reviewee_id) AS num_of_reviewees, t.final_score')
            .from("(#{reviewee_question_scores.to_sql}) AS t").group('t.final_score')
        else
          question_responses_criteria(final_score: final_score).select(
            'COUNT(review_responses.reviewee_id) AS num_of_reviewees',
          ).group('final_score')
        end
      end

      def question_responses_criteria(final_score: false)
        return ReviewResponse.none if final_score && reviewer_type != 'manager'

        score_select =
          if final_score
            'CASE WHEN calibrated_score IS NOT NULL THEN calibrated_score ELSE score END'
          else
            'score'
          end

        review_cycle_id = calibration_question.past_review_question? ? calibration_question.past_review_cycle_id : review_cycle.id
        criteria = ReviewResponse
          .select("#{score_select} AS final_score")
          .joins(reviewer: :reviewer_type)
          .left_joins(reviewee: { employee: :department })
          .where(question_id: calibration_question.question_id)
          .where(reviewee_employee_id: review_cycle.reviewees.pluck(:employee_id))
          .where(reviewer_types: { reviewer_type: reviewer_type, review_cycle_id: review_cycle_id })
          .where(reviewee_id: scoped_reviewee_ids(Reviewee.where(review_cycle_id: review_cycle_id)))
        where_clauses.reduce(criteria) { |c, clause| c.where(clause) }
      end

      def goal_responses
        reviewee_goal_scores = goal_responses_criteria.select(
          'review_responses.reviewee_id',
        ).group('review_responses.reviewee_id')

        ReviewResponse.unscoped.select('COUNT(t.reviewee_id) AS num_of_reviewees, t.final_score')
          .from("(#{reviewee_goal_scores.to_sql}) AS t").group('t.final_score')
      end

      def goal_responses_criteria
        weighted_score_select =
          if review_cycle.goal_weights_required?
            'SUM(reviewee_goals.weightage * review_responses.score) / SUM(reviewee_goals.weightage) AS final_score'
          else
            'AVG(review_responses.score) AS final_score'
          end
        criteria = ReviewResponse
          .select(weighted_score_select)
          .joins('INNER JOIN goals ON goals.id = review_responses.goal_id')
          .joins('INNER JOIN reviewee_goals ON reviewee_goals.goal_id = goals.id AND reviewee_goals.reviewee_id = review_responses.reviewee_id')
          .joins(reviewer: :reviewer_type)
          .left_joins(reviewee: { employee: :department })
          .where(reviewer_types: { reviewer_type: reviewer_type, review_cycle_id: review_cycle.id })
          .where.not(goal_id: nil)
          .where('reviewee_goals.exclude IS false')
          .where(reviewee_id: scoped_reviewee_ids(review_cycle.reviewees))
        where_clauses.reduce(criteria) { |c, clause| c.where(clause) }
      end

      def competency_responses
        reviewee_competency_scores = competency_responses_criteria.select(
          'review_responses.reviewee_id',
        ).group('review_responses.reviewee_id')

        ReviewResponse.unscoped.select('COUNT(t.reviewee_id) AS num_of_reviewees, t.final_score')
          .from("(#{reviewee_competency_scores.to_sql}) AS t").group('t.final_score')
      end

      def competency_responses_criteria
        weighted_score_select =
          if review_cycle.competency_weights_required?
            'SUM(reviewee_competencies.weightage * review_responses.score) / SUM(reviewee_competencies.weightage)
              AS final_score'
          else
            'AVG(review_responses.score) AS final_score'
          end
        criteria = ReviewResponse
          .select(weighted_score_select).joins(:reviewee_competency, reviewer: :reviewer_type)
          .left_joins(reviewee: { employee: :department })
          .where(reviewer_types: { reviewer_type: reviewer_type, review_cycle_id: review_cycle.id })
          .where.not(reviewee_competency_id: nil)
          .where('reviewee_competencies.exclude IS false')
          .where(reviewee_id: scoped_reviewee_ids(review_cycle.reviewees))
        where_clauses.reduce(criteria) { |c, clause| c.where(clause) }
      end

      def nan_to_zero(num)
        return 0.0 if num.nan?

        num
      end

      def discrete_category_values(cq)
        case cq.column_type
        when 'question', 'custom_column'
          cq.question.options.map(&:value)
        when 'goals_score'
          # reviewer_type = cq.column_name.sub('_goals_score', '')
          # custom attributes is not supported as of now
          rctq = review_cycle.review_cycle_templates.joins(:reviewer_type).find_by(reviewer_types: {reviewer_type: reviewer_type})
            .review_cycle_template_questions.find_by(block_type: 'goals')
          rctq.goal_fields['legend'].keys.map(&:to_s)
        when 'competency_score'
          # reviewer_type = cq.column_name.sub('_competency_score', '')
          # custom attributes is not supported as of now
          rctq = review_cycle.review_cycle_templates.joins(:reviewer_type).find_by(reviewer_types: {reviewer_type: reviewer_type})
            .review_cycle_template_questions.find_by(block_type: 'competency')
          rctq.question.options.map(&:value)
        end
      end

      def continuous_score_ranges(cq)
        case cq.column_type
        when 'goals_score'
          # custom attributes is not supported as of now
          rctq = review_cycle.review_cycle_templates.joins(:reviewer_type).find_by(reviewer_types: {reviewer_type: reviewer_type})
            .review_cycle_template_questions.find_by(block_type: 'goals')
          legend_formula = rctq.goal_fields['legend_formula']
          score_ranges(legend_formula)
        when 'competency_score'
          rctq = review_cycle.review_cycle_templates.joins(:reviewer_type).find_by(reviewer_types: {reviewer_type: reviewer_type})
            .review_cycle_template_questions.find_by(block_type: 'competency')
          legend_formula = rctq.legend_formula
          score_ranges(legend_formula)
        when 'question'
          # only autocalculated question will come inside this
          rctq = cq.question.review_cycle_template_questions.first
          legend_formula = rctq.legend_formula
          score_ranges(legend_formula)
        end
      end

      def score_ranges(legend_formula)
        result = []
        sorted_keys = legend_formula.keys.map(&:to_f).sort
        lower_bound = 0.0

        sorted_keys.each do |key|
          upper_bound = key.round(1)
          result << [lower_bound, upper_bound]
          lower_bound = (upper_bound + 0.1).round(1)
        end
        result
      end

      def scoped_reviewee_ids(reviewee_scope)
        Pundit.policy_scope!(current_user, reviewee_scope.where(confidential: false)).pluck(:id)
      end
    end
  end
end
