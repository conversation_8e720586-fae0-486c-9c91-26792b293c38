module PerformanceReview
  module Phases
    module NotificationHelper
      # status is nil because we don't want to pick skipped notifications
      def launch_notification_recipients
        @_launch_notification_recipients ||= case review_cycle_phase.name
        when 'Write Reviews'
          write_reviews_launch_notification_recipients
        else
          non_write_reviews_launch_notification_recipients
        end
      end

      def reminder_notification_recipients
        @_reminder_notification_recipients ||= case review_cycle_phase.phase_type
        when 'write_reviews'
          ReviewCycleNotificationRecipient.joins(:review_cycle_notification, :employee)
            .where(review_cycle_notifications: { review_cycle_phase_id: review_cycle_phase.id, task_type: 'reminder'})
            .where.not(review_cycle_notifications: { task_executed_at: nil })
            .where(employees: { id: scoped_reviewers.pluck(:employee_id) })
            .where(status: nil)
            .select('employees.*').distinct
        else
          ReviewCycleNotificationRecipient.joins(:review_cycle_notification, :employee)
            .where(review_cycle_notifications: { review_cycle_phase_id: review_cycle_phase.id, task_type: 'reminder' })
            .where.not(review_cycle_notifications: { task_executed_at: nil })
            .where(status: nil)
            .select('employees.*').distinct
        end
      end

      def scoped_reviewees
        @_scoped_reviewees ||= Pundit.policy_scope!(current_user, review_cycle.reviewees)
      end

      def scoped_reviewers
        return @_scoped_reviewers if @_scoped_reviewers.present?

        reviewers = review_cycle.reviewers.joins(:reviewer_type).where(rejected_by_id: nil).where('reviewer_types.approval_required = false').or(
          review_cycle.reviewers.joins(:reviewer_type).where('reviewer_types.approval_required = true').where.not(approver: nil),
        )
        reviewers = filter_scoped_reviewer_by_type(reviewers)
        @_scoped_reviewers = Pundit.policy_scope!(current_user, reviewers)
      end

      def filter_scoped_reviewer_by_type(criteria)
        if review_cycle_phase.reviewer_type.present?
          criteria.where(reviewer_types: { reviewer_type: review_cycle_phase.reviewer_type&.reviewer_type })
        else
          criteria.where(reviewer_types: { standalone_launch: false })
        end
      end

      def write_reviews_launch_notification_recipients
        ReviewCycleNotificationRecipient.joins(:review_cycle_notification, :employee)
          .where(review_cycle_notifications: { review_cycle_phase_id: review_cycle_phase.id, task_type: %w[launch automated] })
          .where.not(review_cycle_notifications: { task_executed_at: nil })
          .where(employees: { id: completed_write_reviews_employees })
          .where('status IS NULL OR status != ?', 'skipped')
          .select('employees.*').distinct
      end

      def non_write_reviews_launch_notification_recipients
        raise 'use #write_reviews_launch_notification_recipients' if review_cycle_phase.name == 'Write Reviews'
        
        ReviewCycleNotificationRecipient.joins(:review_cycle_notification, :employee)
          .where(review_cycle_notifications: { review_cycle_phase_id: review_cycle_phase.id, task_type: %w[launch automated] })
          .where.not(review_cycle_notifications: { task_executed_at: nil })
          .where('status IS NULL OR status != ?', 'skipped')
          .select('employees.*').distinct
      end

      private

      def completed_write_reviews_employees
        reviewer_types_to_check = eligible_reviewer_types_for_write_reviews
        non_manager_reviewers = fetch_non_manager_reviewers

        non_manager_reviewers.pluck(:employee_id).uniq.select do |employee_id|
          employee_has_completed_all_required_reviews?(employee_id, reviewer_types_to_check, non_manager_reviewers)
        end
      end

      def eligible_reviewer_types_for_write_reviews
        review_cycle.reviewer_types
          .where(standalone_launch: false)
          .where.not(reviewer_type: 'manager')
          .pluck(:reviewer_type, :id)
          .to_h
      end

      def fetch_non_manager_reviewers
        all_reviewers = scoped_reviewers.includes(:reviewer_type)
        all_reviewers.reject { |r| r.reviewer_type.reviewer_type == 'manager' }
      end

      def employee_has_completed_all_required_reviews?(employee_id, reviewer_types_to_check, non_manager_reviewers)
        reviews_to_write_count = count_required_reviews_for_employee(employee_id, reviewer_types_to_check, non_manager_reviewers)
        completed_notifications_count = count_completed_notifications_for_employee(employee_id)

        reviews_to_write_count == completed_notifications_count
      end

      def count_required_reviews_for_employee(employee_id, reviewer_types_to_check, non_manager_reviewers)
        reviewer_types_to_check.count do |reviewer_type, id|
          non_manager_reviewers.any? { |r| r.reviewer_type_id == id && r.employee_id == employee_id }
        end
      end

      def count_completed_notifications_for_employee(employee_id)
        ReviewCycleNotificationRecipient.joins(:review_cycle_notification)
          .where(review_cycle_notifications: { review_cycle_phase_id: review_cycle_phase.id, task_type: %w[launch automated], }, employee_id: employee_id)
          .where.not(review_cycle_notifications: { task_executed_at: nil })
          .where('status IS NULL OR status != ?', 'skipped')
          .distinct
          .count(:reviewer_type_id)
      end
    end
  end
end